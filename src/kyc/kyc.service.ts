import {
  Injectable,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKycDto, IdentityType, KycStatus } from './dto/create-kyc.dto';
import { UpdateKycDto } from './dto/update-kyc.dto';
import { KycResponseDto, KycStatusResponseDto } from './dto/kyc-response.dto';

@Injectable()
export class KycService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Initiates KY<PERSON> process for user with enhanced provider integration
   */
  async initiateKyc(
    createKycDto: CreateKycDto,
    userId: string,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists dan aktif
      await this.validateUserExists(userId);

      // <PERSON>k apakah user sudah memiliki KYC
      await this.checkExistingKyc(userId);

      // Validasi data KYC
      this.validateKycData(createKycDto);

      // Determine provider - default to MANUAL if not specified
      const provider = createKycDto.provider?.trim() || 'MANUAL';

      // Create KYC data with enhanced provider handling
      const kyc = await this.prisma.kyc.create({
        data: {
          identityType: createKycDto.identityType,
          identityNumber: createKycDto.identityNumber.trim(),
          birthdate: new Date(createKycDto.birthdate),
          birthplace: createKycDto.birthplace.trim(),
          address: createKycDto.address.trim(),
          state: createKycDto.state.trim(),
          country: createKycDto.country.trim(),
          zipNumber: createKycDto.zipNumber.trim(),
          phoneNumber: createKycDto.phoneNumber.trim(),
          fileName: createKycDto.fileName?.trim(),
          provider: provider,
          userId: userId,
          status: KycStatus.PENDING,
        },
      });

      // Integrate with third-party providers
      if (provider === 'SUMSUB') {
        await this.initiateSumsubVerification(kyc);
      }

      return new KycResponseDto({
        id: kyc.id,
        identityType: kyc.identityType as IdentityType,
        identityNumber: kyc.identityNumber,
        birthdate: kyc.birthdate,
        birthplace: kyc.birthplace,
        address: kyc.address,
        state: kyc.state,
        country: kyc.country,
        zipNumber: kyc.zipNumber,
        phoneNumber: kyc.phoneNumber,
        status: kyc.status as KycStatus,
        provider: kyc.provider,
        fileName: kyc.fileName,
        userId: kyc.userId,
        createdAt: kyc.createdAt,
        updatedAt: kyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to initiate KYC process');
    }
  }

  /**
   * Membuat data KYC baru untuk user (Legacy method)
   * @deprecated Use initiateKyc instead for new implementations
   */
  async create(
    createKycDto: CreateKycDto,
    userId: string,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists dan aktif
      await this.validateUserExists(userId);

      // Cek apakah user sudah memiliki KYC
      await this.checkExistingKyc(userId);

      // Validasi data KYC
      this.validateKycData(createKycDto);

      // Create KYC data
      const kyc = await this.prisma.kyc.create({
        data: {
          identityType: createKycDto.identityType,
          identityNumber: createKycDto.identityNumber.trim(),
          birthdate: new Date(createKycDto.birthdate),
          birthplace: createKycDto.birthplace.trim(),
          address: createKycDto.address.trim(),
          state: createKycDto.state.trim(),
          country: createKycDto.country.trim(),
          zipNumber: createKycDto.zipNumber.trim(),
          phoneNumber: createKycDto.phoneNumber.trim(),
          fileName: createKycDto.fileName?.trim(),
          provider: createKycDto.provider?.trim(),
          userId: userId,
          status: KycStatus.PENDING,
        },
      });

      return new KycResponseDto({
        id: kyc.id,
        identityType: kyc.identityType as IdentityType,
        identityNumber: kyc.identityNumber,
        birthdate: kyc.birthdate,
        birthplace: kyc.birthplace,
        address: kyc.address,
        state: kyc.state,
        country: kyc.country,
        zipNumber: kyc.zipNumber,
        phoneNumber: kyc.phoneNumber,
        status: kyc.status as KycStatus,
        provider: kyc.provider,
        fileName: kyc.fileName,
        userId: kyc.userId,
        createdAt: kyc.createdAt,
        updatedAt: kyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to create KYC');
    }
  }

  /**
   * Mendapatkan data KYC berdasarkan user ID
   */
  async findByUserId(userId: string): Promise<KycResponseDto> {
    try {
      // Validasi user exists
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      return new KycResponseDto({
        id: kyc.id,
        identityType: kyc.identityType as IdentityType,
        identityNumber: kyc.identityNumber,
        birthdate: kyc.birthdate,
        birthplace: kyc.birthplace,
        address: kyc.address,
        state: kyc.state,
        country: kyc.country,
        zipNumber: kyc.zipNumber,
        phoneNumber: kyc.phoneNumber,
        status: kyc.status as KycStatus,
        provider: kyc.provider,
        fileName: kyc.fileName,
        userId: kyc.userId,
        createdAt: kyc.createdAt,
        updatedAt: kyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve KYC data');
    }
  }

  /**
   * Update data KYC
   */
  async update(
    userId: string,
    updateKycDto: UpdateKycDto,
    isAdmin: boolean = false,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists
      await this.validateUserExists(userId);

      // Cek apakah KYC exists
      const existingKyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!existingKyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      // Validasi permission untuk update
      this.validateUpdatePermission(existingKyc.status as KycStatus, isAdmin);

      // Validasi data yang akan diupdate
      if (updateKycDto.identityNumber || updateKycDto.identityType) {
        this.validateKycData(updateKycDto as CreateKycDto);
      }

      // Prepare update data
      const updateData: any = {};

      // Only allow certain fields to be updated by users
      const allowedUserFields = [
        'identityType',
        'identityNumber',
        'birthdate',
        'birthplace',
        'address',
        'state',
        'country',
        'zipNumber',
        'phoneNumber',
        'fileName',
      ];

      for (const [key, value] of Object.entries(updateKycDto)) {
        if (value !== undefined && value !== null) {
          // Admin can update status and provider
          if (isAdmin || allowedUserFields.includes(key)) {
            if (key === 'birthdate') {
              updateData[key] = new Date(value as string);
            } else if (typeof value === 'string') {
              updateData[key] = value.trim();
            } else {
              updateData[key] = value;
            }
          }
        }
      }

      // If user is updating their own data while status is not PENDING, reset to PENDING
      if (
        !isAdmin &&
        Object.keys(updateData).some((key) => allowedUserFields.includes(key))
      ) {
        if (existingKyc.status !== 'PENDING') {
          updateData.status = KycStatus.PENDING;
        }
      }

      const updatedKyc = await this.prisma.kyc.update({
        where: { userId },
        data: updateData,
      });

      return new KycResponseDto({
        id: updatedKyc.id,
        identityType: updatedKyc.identityType as IdentityType,
        identityNumber: updatedKyc.identityNumber,
        birthdate: updatedKyc.birthdate,
        birthplace: updatedKyc.birthplace,
        address: updatedKyc.address,
        state: updatedKyc.state,
        country: updatedKyc.country,
        zipNumber: updatedKyc.zipNumber,
        phoneNumber: updatedKyc.phoneNumber,
        status: updatedKyc.status as KycStatus,
        provider: updatedKyc.provider,
        fileName: updatedKyc.fileName,
        userId: updatedKyc.userId,
        createdAt: updatedKyc.createdAt,
        updatedAt: updatedKyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to update KYC data');
    }
  }

  /**
   * Mendapatkan status KYC
   */
  async getStatus(userId: string): Promise<KycStatusResponseDto> {
    try {
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
        select: {
          status: true,
          updatedAt: true,
        },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      const statusMessage = this.getStatusMessage(kyc.status as KycStatus);

      return new KycStatusResponseDto(
        kyc.status as KycStatus,
        statusMessage,
        kyc.updatedAt,
      );
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve KYC status');
    }
  }

  /**
   * Delete KYC data (admin only)
   */
  async delete(userId: string): Promise<{ message: string }> {
    try {
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      await this.prisma.kyc.delete({
        where: { userId },
      });

      return { message: 'Data KYC berhasil dihapus' };
    } catch (error) {
      this.handleServiceError(error, 'Failed to delete KYC data');
    }
  }

  /**
   * Validasi user exists dan aktif
   */
  private async validateUserExists(userId: string) {
    if (!userId || typeof userId !== 'string') {
      throw new BadRequestException('User ID tidak valid');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User tidak ditemukan');
    }

    if (user.status !== 'ACTIVE') {
      throw new ForbiddenException('Akun user tidak aktif');
    }

    return user;
  }

  /**
   * Cek apakah user sudah memiliki KYC
   */
  private async checkExistingKyc(userId: string): Promise<void> {
    const existingKyc = await this.prisma.kyc.findUnique({
      where: { userId },
    });

    if (existingKyc) {
      throw new ConflictException('User sudah memiliki data KYC');
    }
  }

  /**
   * Validasi data KYC
   */
  private validateKycData(kycData: CreateKycDto | UpdateKycDto): void {
    // Validasi tanggal lahir tidak di masa depan
    if (kycData.birthdate) {
      const birthDate = new Date(kycData.birthdate);
      const now = new Date();

      if (birthDate >= now) {
        throw new BadRequestException(
          'Tanggal lahir tidak boleh di masa depan',
        );
      }

      // Validasi umur minimal 17 tahun untuk KYC
      const minAge = 17;
      const ageInYears = now.getFullYear() - birthDate.getFullYear();
      if (ageInYears < minAge) {
        throw new BadRequestException(
          `Umur minimal ${minAge} tahun untuk melakukan KYC`,
        );
      }
    }

    // Validasi format nomor identitas berdasarkan jenis
    if (kycData.identityNumber && kycData.identityType) {
      this.validateIdentityNumber(kycData.identityNumber, kycData.identityType);
    }

    // Validasi nomor telepon Indonesia
    if (kycData.phoneNumber) {
      this.validatePhoneNumber(kycData.phoneNumber);
    }
  }

  /**
   * Validasi nomor identitas berdasarkan jenisnya
   */
  private validateIdentityNumber(
    identityNumber: string,
    identityType: IdentityType,
  ): void {
    switch (identityType) {
      case IdentityType.KTP:
        if (!/^\d{16}$/.test(identityNumber)) {
          throw new BadRequestException('Nomor KTP harus 16 digit angka');
        }
        break;
      case IdentityType.PASSPORT:
        if (!/^[A-Z]\d{7}$/.test(identityNumber)) {
          throw new BadRequestException(
            'Format passport tidak valid (contoh: ********)',
          );
        }
        break;
      case IdentityType.DRIVER_LICENSE:
        if (!/^[A-Z0-9]{10,15}$/.test(identityNumber)) {
          throw new BadRequestException('Format SIM tidak valid');
        }
        break;
      case IdentityType.SIM:
        if (!/^[A-Z0-9]{10,15}$/.test(identityNumber)) {
          throw new BadRequestException('Format SIM tidak valid');
        }
        break;
    }
  }

  /**
   * Validasi nomor telepon Indonesia
   */
  private validatePhoneNumber(phoneNumber: string): void {
    if (!phoneNumber.startsWith('+62')) {
      throw new BadRequestException(
        'Nomor telepon harus menggunakan kode negara Indonesia (+62)',
      );
    }
  }

  /**
   * Validasi permission untuk update
   */
  private validateUpdatePermission(
    currentStatus: KycStatus,
    isAdmin: boolean,
  ): void {
    // Admin bisa update status apapun
    if (isAdmin) return;

    // User hanya bisa update jika status PENDING, REJECTED, atau DENIED
    const allowedStatuses = [
      KycStatus.PENDING,
      KycStatus.REJECTED,
      KycStatus.DENIED,
    ];

    if (!allowedStatuses.includes(currentStatus)) {
      throw new ForbiddenException(
        'Data KYC tidak dapat diubah karena sedang dalam proses verifikasi atau sudah disetujui',
      );
    }
  }

  /**
   * Get status message
   */
  private getStatusMessage(status: KycStatus): string {
    const statusMessages = {
      [KycStatus.PENDING]: 'KYC sedang dalam proses verifikasi',
      [KycStatus.APPROVED]: 'KYC telah disetujui',
      [KycStatus.REJECTED]:
        'KYC ditolak, silakan perbaiki data dan kirim ulang',
      [KycStatus.DENIED]: 'KYC ditolak permanen',
      [KycStatus.CANCELED]: 'KYC dibatalkan',
      [KycStatus.MANUAL_REVIEW]: 'KYC memerlukan review manual',
      [KycStatus.LOCKED]: 'KYC terkunci',
    };

    return statusMessages[status] || 'Status tidak diketahui';
  }

  /**
   * Initiate Sumsub verification process
   */
  private async initiateSumsubVerification(kyc: any): Promise<void> {
    try {
      // TODO: Implement actual Sumsub API integration
      // This is a placeholder for the Sumsub integration logic
      console.log(`Initiating Sumsub verification for KYC ID: ${kyc.id}`);

      // Example of what the Sumsub integration might look like:
      // const sumsubClient = new SumsubClient(process.env.SUMSUB_API_KEY);
      // const applicant = await sumsubClient.createApplicant({
      //   externalUserId: kyc.userId,
      //   info: {
      //     firstName: kyc.firstName,
      //     lastName: kyc.lastName,
      //     dob: kyc.birthdate,
      //     country: kyc.country,
      //   }
      // });

      // Update KYC with Sumsub applicant ID
      // await this.prisma.kyc.update({
      //   where: { id: kyc.id },
      //   data: {
      //     provider: 'SUMSUB',
      //     externalId: applicant.id
      //   }
      // });

    } catch (error) {
      console.error('Failed to initiate Sumsub verification:', error);
      // Don't throw error to prevent KYC creation failure
      // Log the error and continue with manual verification
    }
  }

  /**
   * Handle Sumsub webhook status updates
   */
  async handleSumsubWebhook(webhookData: any): Promise<void> {
    try {
      const { applicantId, reviewStatus, externalUserId } = webhookData;

      if (!externalUserId) {
        throw new BadRequestException('External user ID is required');
      }

      // Find KYC record by user ID
      const kyc = await this.prisma.kyc.findUnique({
        where: { userId: externalUserId },
      });

      if (!kyc) {
        throw new NotFoundException(`KYC record not found for user: ${externalUserId}`);
      }

      // Map Sumsub status to our KYC status
      const kycStatus = this.mapSumsubStatusToKycStatus(reviewStatus);

      // Update KYC status
      await this.prisma.kyc.update({
        where: { userId: externalUserId },
        data: {
          status: kycStatus,
          provider: 'SUMSUB',
          // Store additional Sumsub data if needed
          // externalId: applicantId,
        },
      });

      console.log(`Updated KYC status for user ${externalUserId} to ${kycStatus}`);

    } catch (error) {
      console.error('Failed to handle Sumsub webhook:', error);
      throw error;
    }
  }

  /**
   * Map Sumsub review status to KYC status
   */
  private mapSumsubStatusToKycStatus(sumsubStatus: string): KycStatus {
    const statusMap: Record<string, KycStatus> = {
      'completed': KycStatus.APPROVED,
      'approved': KycStatus.APPROVED,
      'rejected': KycStatus.REJECTED,
      'pending': KycStatus.PENDING,
      'init': KycStatus.PENDING,
      'queued': KycStatus.PENDING,
      'onHold': KycStatus.MANUAL_REVIEW,
      'final_rejected': KycStatus.DENIED,
    };

    return statusMap[sumsubStatus] || KycStatus.MANUAL_REVIEW;
  }

  /**
   * Get KYC status for Sumsub integration
   */
  async getSumsubKycStatus(userId: string): Promise<{
    status: KycStatus;
    canProceed: boolean;
    message: string;
  }> {
    try {
      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
        select: {
          status: true,
          provider: true,
          updatedAt: true,
        },
      });

      if (!kyc) {
        return {
          status: KycStatus.PENDING,
          canProceed: false,
          message: 'No KYC record found. Please initiate KYC process.',
        };
      }

      const status = kyc.status as KycStatus;
      const canProceed = status === KycStatus.APPROVED;
      const message = this.getStatusMessage(status);

      return {
        status,
        canProceed,
        message,
      };
    } catch (error) {
      this.handleServiceError(error, 'Failed to get Sumsub KYC status');
    }
  }

  /**
   * Handle service errors
   */
  private handleServiceError(error: any, defaultMessage: string): never {
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException ||
      error instanceof NotFoundException ||
      error instanceof ForbiddenException
    ) {
      throw error;
    }

    console.error('KYC Service Error:', error);

    if (error.code === 'P2002') {
      throw new ConflictException('Data KYC sudah ada');
    }

    if (error.code === 'P2025') {
      throw new NotFoundException('Data tidak ditemukan');
    }

    throw new InternalServerErrorException(defaultMessage);
  }
}

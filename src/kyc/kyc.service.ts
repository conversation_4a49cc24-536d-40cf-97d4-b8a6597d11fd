import {
  Injectable,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKycDto, IdentityType, KycStatus } from './dto/create-kyc.dto';
import { UpdateKycDto } from './dto/update-kyc.dto';
import { KycResponseDto, KycStatusResponseDto } from './dto/kyc-response.dto';
import {
  KycEntity,
  UserEntity,
  KycStatusUpdateData,
  KycVerificationStatus,
  ValidationResult,
} from './interfaces/kyc.interface';
import {
  KYC_BUSINESS_RULES,
  VALID_USER_STATUSES,
  USER_EDITABLE_STATUSES,
  LOCKED_STATUSES,
  VALID_STATUS_TRANSITIONS,
  ADMIN_ONLY_TRANSITIONS,
  STATUS_MESSAGES,
  SUMSUB_STATUS_MAPPING,
  VALIDATION_PATTERNS,
  INVALID_PATTERNS,
  ERROR_MESSAGES,
} from './constants/kyc.constants';

@Injectable()
export class KycService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Initiates KYC process for user with enhanced provider integration
   */
  async initiateKyc(
    createKycDto: CreateKycDto,
    userId: string,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists dan aktif
      await this.validateUserExists(userId);

      // Cek apakah user sudah memiliki KYC
      await this.checkExistingKyc(userId);

      // Validasi data KYC
      this.validateKycData(createKycDto);

      // Determine provider - default to MANUAL if not specified
      const provider = createKycDto.provider?.trim() || 'MANUAL';

      // Create KYC data with enhanced provider handling
      const kyc = await this.prisma.kyc.create({
        data: {
          identityType: createKycDto.identityType,
          identityNumber: createKycDto.identityNumber.trim(),
          birthdate: new Date(createKycDto.birthdate),
          birthplace: createKycDto.birthplace.trim(),
          address: createKycDto.address.trim(),
          state: createKycDto.state.trim(),
          country: createKycDto.country.trim(),
          zipNumber: createKycDto.zipNumber.trim(),
          phoneNumber: createKycDto.phoneNumber.trim(),
          fileName: createKycDto.fileName?.trim(),
          provider: provider,
          userId: userId,
          status: KycStatus.PENDING,
        },
      });

      // Integrate with third-party providers
      if (provider === 'SUMSUB') {
        await this.initiateSumsubVerification(kyc);
      }

      return new KycResponseDto({
        id: kyc.id,
        identityType: kyc.identityType as IdentityType,
        identityNumber: kyc.identityNumber,
        birthdate: kyc.birthdate,
        birthplace: kyc.birthplace,
        address: kyc.address,
        state: kyc.state,
        country: kyc.country,
        zipNumber: kyc.zipNumber,
        phoneNumber: kyc.phoneNumber,
        status: kyc.status as KycStatus,
        provider: kyc.provider,
        fileName: kyc.fileName,
        userId: kyc.userId,
        createdAt: kyc.createdAt,
        updatedAt: kyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to initiate KYC process');
    }
  }

  /**
   * Membuat data KYC baru untuk user (Legacy method)
   * @deprecated Use initiateKyc instead for new implementations
   */
  async create(
    createKycDto: CreateKycDto,
    userId: string,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists dan aktif
      await this.validateUserExists(userId);

      // Cek apakah user sudah memiliki KYC
      await this.checkExistingKyc(userId);

      // Validasi data KYC
      this.validateKycData(createKycDto);

      // Create KYC data
      const kyc = await this.prisma.kyc.create({
        data: {
          identityType: createKycDto.identityType,
          identityNumber: createKycDto.identityNumber.trim(),
          birthdate: new Date(createKycDto.birthdate),
          birthplace: createKycDto.birthplace.trim(),
          address: createKycDto.address.trim(),
          state: createKycDto.state.trim(),
          country: createKycDto.country.trim(),
          zipNumber: createKycDto.zipNumber.trim(),
          phoneNumber: createKycDto.phoneNumber.trim(),
          fileName: createKycDto.fileName?.trim(),
          provider: createKycDto.provider?.trim(),
          userId: userId,
          status: KycStatus.PENDING,
        },
      });

      return new KycResponseDto({
        id: kyc.id,
        identityType: kyc.identityType as IdentityType,
        identityNumber: kyc.identityNumber,
        birthdate: kyc.birthdate,
        birthplace: kyc.birthplace,
        address: kyc.address,
        state: kyc.state,
        country: kyc.country,
        zipNumber: kyc.zipNumber,
        phoneNumber: kyc.phoneNumber,
        status: kyc.status as KycStatus,
        provider: kyc.provider,
        fileName: kyc.fileName,
        userId: kyc.userId,
        createdAt: kyc.createdAt,
        updatedAt: kyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to create KYC');
    }
  }

  /**
   * Mendapatkan data KYC berdasarkan user ID
   */
  async findByUserId(userId: string): Promise<KycResponseDto> {
    try {
      // Validasi user exists
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      return new KycResponseDto({
        id: kyc.id,
        identityType: kyc.identityType as IdentityType,
        identityNumber: kyc.identityNumber,
        birthdate: kyc.birthdate,
        birthplace: kyc.birthplace,
        address: kyc.address,
        state: kyc.state,
        country: kyc.country,
        zipNumber: kyc.zipNumber,
        phoneNumber: kyc.phoneNumber,
        status: kyc.status as KycStatus,
        provider: kyc.provider,
        fileName: kyc.fileName,
        userId: kyc.userId,
        createdAt: kyc.createdAt,
        updatedAt: kyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve KYC data');
    }
  }

  /**
   * Update data KYC
   */
  async update(
    userId: string,
    updateKycDto: UpdateKycDto,
    isAdmin: boolean = false,
  ): Promise<KycResponseDto> {
    try {
      // Validasi user exists
      await this.validateUserExists(userId);

      // Cek apakah KYC exists
      const existingKyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!existingKyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      // Validasi permission untuk update
      this.validateUpdatePermission(existingKyc.status as KycStatus, isAdmin);

      // Validasi data yang akan diupdate
      if (updateKycDto.identityNumber || updateKycDto.identityType) {
        this.validateKycData(updateKycDto as CreateKycDto);
      }

      // Prepare update data
      const updateData: any = {};

      // Only allow certain fields to be updated by users
      const allowedUserFields = [
        'identityType',
        'identityNumber',
        'birthdate',
        'birthplace',
        'address',
        'state',
        'country',
        'zipNumber',
        'phoneNumber',
        'fileName',
      ];

      for (const [key, value] of Object.entries(updateKycDto)) {
        if (value !== undefined && value !== null) {
          // Admin can update status and provider
          if (isAdmin || allowedUserFields.includes(key)) {
            if (key === 'birthdate') {
              updateData[key] = new Date(value as string);
            } else if (typeof value === 'string') {
              updateData[key] = value.trim();
            } else {
              updateData[key] = value;
            }
          }
        }
      }

      // If user is updating their own data while status is not PENDING, reset to PENDING
      if (
        !isAdmin &&
        Object.keys(updateData).some((key) => allowedUserFields.includes(key))
      ) {
        if (existingKyc.status !== 'PENDING') {
          updateData.status = KycStatus.PENDING;
        }
      }

      const updatedKyc = await this.prisma.kyc.update({
        where: { userId },
        data: updateData,
      });

      return new KycResponseDto({
        id: updatedKyc.id,
        identityType: updatedKyc.identityType as IdentityType,
        identityNumber: updatedKyc.identityNumber,
        birthdate: updatedKyc.birthdate,
        birthplace: updatedKyc.birthplace,
        address: updatedKyc.address,
        state: updatedKyc.state,
        country: updatedKyc.country,
        zipNumber: updatedKyc.zipNumber,
        phoneNumber: updatedKyc.phoneNumber,
        status: updatedKyc.status as KycStatus,
        provider: updatedKyc.provider,
        fileName: updatedKyc.fileName,
        userId: updatedKyc.userId,
        createdAt: updatedKyc.createdAt,
        updatedAt: updatedKyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(error, 'Failed to update KYC data');
    }
  }

  /**
   * Mendapatkan status KYC
   */
  async getStatus(userId: string): Promise<KycStatusResponseDto> {
    try {
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
        select: {
          status: true,
          updatedAt: true,
        },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      const statusMessage = this.getStatusMessage(kyc.status as KycStatus);

      return new KycStatusResponseDto(
        kyc.status as KycStatus,
        statusMessage,
        kyc.updatedAt,
      );
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve KYC status');
    }
  }

  /**
   * Delete KYC data (admin only)
   */
  async delete(userId: string): Promise<{ message: string }> {
    try {
      await this.validateUserExists(userId);

      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      await this.prisma.kyc.delete({
        where: { userId },
      });

      return { message: 'Data KYC berhasil dihapus' };
    } catch (error) {
      this.handleServiceError(error, 'Failed to delete KYC data');
    }
  }

  /**
   * Comprehensive user validation with enhanced error handling
   */
  private async validateUserExists(userId: string): Promise<UserEntity> {
    // Input validation
    if (!userId) {
      throw new BadRequestException(ERROR_MESSAGES.USER_ID_REQUIRED);
    }

    if (typeof userId !== 'string') {
      throw new BadRequestException(ERROR_MESSAGES.USER_ID_INVALID);
    }

    if (userId.trim().length === 0) {
      throw new BadRequestException(ERROR_MESSAGES.USER_ID_EMPTY);
    }

    // Sanitize input
    const sanitizedUserId = userId.trim();

    try {
      const user = await this.prisma.user.findUnique({
        where: { id: sanitizedUserId },
        select: {
          id: true,
          status: true,
          email: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new NotFoundException(
          `${ERROR_MESSAGES.USER_NOT_FOUND}: ${sanitizedUserId}`,
        );
      }

      // Enhanced status validation using constants
      if (!VALID_USER_STATUSES.includes(user.status)) {
        throw new ForbiddenException(
          `${ERROR_MESSAGES.USER_NOT_ACTIVE}. Current status: ${user.status}`,
        );
      }

      return user as UserEntity;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      console.error('Database error during user validation:', error);
      throw new InternalServerErrorException('Failed to validate user');
    }
  }

  /**
   * Cek apakah user sudah memiliki KYC
   */
  private async checkExistingKyc(userId: string): Promise<void> {
    const existingKyc = await this.prisma.kyc.findUnique({
      where: { userId },
    });

    if (existingKyc) {
      throw new ConflictException('User sudah memiliki data KYC');
    }
  }

  /**
   * Enhanced KYC data validation with comprehensive business rules
   */
  private validateKycData(kycData: CreateKycDto | UpdateKycDto): void {
    // Validate birthdate with enhanced logic
    if (kycData.birthdate) {
      this.validateBirthdate(kycData.birthdate);
    }

    // Validate identity information
    if (kycData.identityNumber && kycData.identityType) {
      this.validateIdentityNumber(kycData.identityNumber, kycData.identityType);
    }

    // Validate phone number with enhanced rules
    if (kycData.phoneNumber) {
      this.validatePhoneNumber(kycData.phoneNumber);
    }

    // Validate address information
    if (kycData.address) {
      this.validateAddress(kycData.address);
    }

    // Validate location information
    if (kycData.country) {
      this.validateCountry(kycData.country);
    }

    if (kycData.zipNumber) {
      this.validateZipCode(kycData.zipNumber, kycData.country);
    }

    // Validate file information
    if (kycData.fileName) {
      this.validateFileName(kycData.fileName);
    }

    // Validate provider
    if (kycData.provider) {
      this.validateProvider(kycData.provider);
    }
  }

  /**
   * Enhanced birthdate validation
   */
  private validateBirthdate(birthdate: string): void {
    const birthDate = new Date(birthdate);
    const now = new Date();

    // Check if date is valid
    if (isNaN(birthDate.getTime())) {
      throw new BadRequestException('Invalid birthdate format');
    }

    // Check if date is not in the future
    if (birthDate >= now) {
      throw new BadRequestException('Birthdate cannot be in the future');
    }

    // Check minimum age (17 years)
    const minAge = 17;
    const maxAge = 120; // Maximum reasonable age

    const ageInYears = now.getFullYear() - birthDate.getFullYear();
    const monthDiff = now.getMonth() - birthDate.getMonth();
    const dayDiff = now.getDate() - birthDate.getDate();

    let actualAge = ageInYears;
    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      actualAge--;
    }

    if (actualAge < minAge) {
      throw new BadRequestException(
        `Minimum age requirement is ${minAge} years. Current age: ${actualAge}`,
      );
    }

    if (actualAge > maxAge) {
      throw new BadRequestException(
        `Age seems unrealistic. Please verify birthdate. Calculated age: ${actualAge}`,
      );
    }
  }

  /**
   * Validasi nomor identitas berdasarkan jenisnya
   */
  private validateIdentityNumber(
    identityNumber: string,
    identityType: IdentityType,
  ): void {
    switch (identityType) {
      case IdentityType.KTP:
        if (!/^\d{16}$/.test(identityNumber)) {
          throw new BadRequestException('Nomor KTP harus 16 digit angka');
        }
        break;
      case IdentityType.PASSPORT:
        if (!/^[A-Z]\d{7}$/.test(identityNumber)) {
          throw new BadRequestException(
            'Format passport tidak valid (contoh: ********)',
          );
        }
        break;
      case IdentityType.DRIVER_LICENSE:
        if (!/^[A-Z0-9]{10,15}$/.test(identityNumber)) {
          throw new BadRequestException('Format SIM tidak valid');
        }
        break;
      case IdentityType.SIM:
        if (!/^[A-Z0-9]{10,15}$/.test(identityNumber)) {
          throw new BadRequestException('Format SIM tidak valid');
        }
        break;
    }
  }

  /**
   * Enhanced phone number validation
   */
  private validatePhoneNumber(phoneNumber: string): void {
    const trimmedPhone = phoneNumber.trim();

    // Check if phone number starts with +62 (Indonesia)
    if (!trimmedPhone.startsWith('+62')) {
      throw new BadRequestException(
        'Phone number must use Indonesian country code (+62)',
      );
    }

    // Validate phone number format and length
    const phoneRegex = /^\+62[0-9]{8,13}$/;
    if (!phoneRegex.test(trimmedPhone)) {
      throw new BadRequestException(
        'Invalid Indonesian phone number format. Expected: +62xxxxxxxxx',
      );
    }

    // Check for common invalid patterns
    const invalidPatterns = [
      /^\+620{8,}$/, // All zeros after +62
      /^\+621{8,}$/, // All ones after +62
    ];

    for (const pattern of invalidPatterns) {
      if (pattern.test(trimmedPhone)) {
        throw new BadRequestException('Phone number contains invalid pattern');
      }
    }
  }

  /**
   * Validate address information
   */
  private validateAddress(address: string): void {
    const trimmedAddress = address.trim();

    if (trimmedAddress.length < 10) {
      throw new BadRequestException(
        'Address must be at least 10 characters long',
      );
    }

    if (trimmedAddress.length > 500) {
      throw new BadRequestException('Address must not exceed 500 characters');
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /^(.)\1{9,}$/, // Repeated characters
      /test|dummy|fake|sample/i, // Test data
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(trimmedAddress)) {
        throw new BadRequestException(
          'Address appears to contain invalid or test data',
        );
      }
    }
  }

  /**
   * Validate country information
   */
  private validateCountry(country: string): void {
    const trimmedCountry = country.trim();

    // For now, primarily support Indonesia
    const supportedCountries = ['IDN', 'INDONESIA', 'ID'];

    if (!supportedCountries.includes(trimmedCountry.toUpperCase())) {
      throw new BadRequestException(
        'Currently only Indonesian residents are supported for KYC verification',
      );
    }
  }

  /**
   * Validate ZIP code based on country
   */
  private validateZipCode(zipCode: string, country?: string): void {
    const trimmedZip = zipCode.trim();

    // Indonesian postal code validation
    if (
      !country ||
      country.toUpperCase().includes('IDN') ||
      country.toUpperCase().includes('INDONESIA')
    ) {
      const indonesianZipRegex = /^[0-9]{5}$/;
      if (!indonesianZipRegex.test(trimmedZip)) {
        throw new BadRequestException(
          'Indonesian postal code must be exactly 5 digits',
        );
      }

      // Check for invalid postal codes
      if (trimmedZip === '00000' || trimmedZip === '99999') {
        throw new BadRequestException('Invalid postal code');
      }
    }
  }

  /**
   * Validate file name
   */
  private validateFileName(fileName: string): void {
    const trimmedFileName = fileName.trim();

    // Check file extension
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf'];
    const hasValidExtension = allowedExtensions.some((ext) =>
      trimmedFileName.toLowerCase().endsWith(ext),
    );

    if (!hasValidExtension) {
      throw new BadRequestException(
        `File must have one of these extensions: ${allowedExtensions.join(', ')}`,
      );
    }

    // Check file name length
    if (trimmedFileName.length > 255) {
      throw new BadRequestException('File name must not exceed 255 characters');
    }

    // Check for suspicious file names
    const suspiciousPatterns = [
      /test|dummy|fake|sample/i,
      /^temp/i,
      /\.(exe|bat|cmd|scr|vbs)$/i, // Executable files
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(trimmedFileName)) {
        throw new BadRequestException(
          'File name appears to be invalid or suspicious',
        );
      }
    }
  }

  /**
   * Validate KYC provider
   */
  private validateProvider(provider: string): void {
    const trimmedProvider = provider.trim().toUpperCase();
    const supportedProviders = ['MANUAL', 'SUMSUB'];

    if (!supportedProviders.includes(trimmedProvider)) {
      throw new BadRequestException(
        `Unsupported KYC provider. Supported providers: ${supportedProviders.join(', ')}`,
      );
    }
  }

  /**
   * Enhanced permission validation for KYC updates using constants
   */
  private validateUpdatePermission(
    currentStatus: KycStatus,
    isAdmin: boolean,
  ): void {
    // Admin can update any status
    if (isAdmin) {
      console.log('Admin update permission granted');
      return;
    }

    if (LOCKED_STATUSES.includes(currentStatus)) {
      throw new ForbiddenException(
        `${ERROR_MESSAGES.UPDATE_FORBIDDEN}: ${currentStatus}. Please contact support if changes are needed.`,
      );
    }

    if (!USER_EDITABLE_STATUSES.includes(currentStatus)) {
      throw new ForbiddenException(
        `${ERROR_MESSAGES.UPDATE_FORBIDDEN}: ${currentStatus}`,
      );
    }

    console.log(`User update permission granted for status: ${currentStatus}`);
  }

  /**
   * Validate status transitions for business logic compliance
   */
  private validateStatusTransition(
    fromStatus: KycStatus,
    toStatus: KycStatus,
    isAdmin: boolean,
  ): void {
    // Define valid status transitions
    const validTransitions: Record<KycStatus, KycStatus[]> = {
      [KycStatus.PENDING]: [
        KycStatus.APPROVED,
        KycStatus.REJECTED,
        KycStatus.MANUAL_REVIEW,
        KycStatus.CANCELED,
      ],
      [KycStatus.MANUAL_REVIEW]: [
        KycStatus.APPROVED,
        KycStatus.REJECTED,
        KycStatus.DENIED,
        KycStatus.PENDING,
      ],
      [KycStatus.REJECTED]: [
        KycStatus.PENDING, // User can resubmit
        KycStatus.DENIED, // Admin can permanently deny
        KycStatus.APPROVED, // Admin can approve after review
      ],
      [KycStatus.DENIED]: [
        KycStatus.PENDING, // Only admin can reset to pending
      ],
      [KycStatus.APPROVED]: [
        KycStatus.LOCKED, // Admin can lock approved KYC
        KycStatus.MANUAL_REVIEW, // Admin can flag for review
      ],
      [KycStatus.CANCELED]: [
        KycStatus.PENDING, // Admin can reactivate
      ],
      [KycStatus.LOCKED]: [
        KycStatus.APPROVED, // Admin can unlock
        KycStatus.MANUAL_REVIEW, // Admin can send for review
      ],
    };

    const allowedTransitions = validTransitions[fromStatus] || [];

    if (!allowedTransitions.includes(toStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${fromStatus} to ${toStatus}`,
      );
    }

    // Additional restrictions for non-admin users
    if (!isAdmin) {
      const userRestrictedTransitions = [
        { from: KycStatus.DENIED, to: KycStatus.PENDING },
        { from: KycStatus.APPROVED, to: KycStatus.LOCKED },
        { from: KycStatus.CANCELED, to: KycStatus.PENDING },
      ];

      const isRestricted = userRestrictedTransitions.some(
        (transition) =>
          transition.from === fromStatus && transition.to === toStatus,
      );

      if (isRestricted) {
        throw new ForbiddenException(
          `Status transition from ${fromStatus} to ${toStatus} requires admin privileges`,
        );
      }
    }

    console.log(`Valid status transition: ${fromStatus} → ${toStatus}`);
  }

  /**
   * Get status message using constants
   */
  private getStatusMessage(status: KycStatus): string {
    return STATUS_MESSAGES[status] || 'Unknown status';
  }

  /**
   * Prepare KYC for Sumsub verification (frontend integration)
   */
  private async initiateSumsubVerification(kyc: any): Promise<void> {
    try {
      // Since Sumsub is handled on the frontend, we just mark the KYC as ready for Sumsub
      console.log(
        `KYC prepared for Sumsub verification - ID: ${kyc.id}, User: ${kyc.userId}`,
      );

      // The frontend will handle the Sumsub SDK and send status updates back to us
      // No direct API integration needed here
    } catch (error) {
      console.error('Failed to prepare Sumsub verification:', error);
      // Don't throw error to prevent KYC creation failure
    }
  }

  /**
   * Handle Sumsub status updates from frontend
   */
  async updateKycStatusFromFrontend(
    userId: string,
    statusData: {
      reviewStatus: string;
      applicantId?: string;
      additionalData?: any;
    },
  ): Promise<KycResponseDto> {
    try {
      // Validate user exists
      await this.validateUserExists(userId);

      // Find KYC record by user ID
      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
      });

      if (!kyc) {
        throw new NotFoundException('Data KYC tidak ditemukan');
      }

      // Only allow status updates for PENDING or MANUAL_REVIEW status
      if (kyc.status !== 'PENDING' && kyc.status !== 'MANUAL_REVIEW') {
        throw new ForbiddenException(
          'KYC status can only be updated when status is PENDING or MANUAL_REVIEW',
        );
      }

      // Map Sumsub status to our KYC status
      const kycStatus = this.mapSumsubStatusToKycStatus(
        statusData.reviewStatus,
      );

      // Update KYC status
      const updatedKyc = await this.prisma.kyc.update({
        where: { userId },
        data: {
          status: kycStatus,
          provider: 'SUMSUB',
        },
      });

      console.log(`Updated KYC status for user ${userId} to ${kycStatus}`);

      return new KycResponseDto({
        id: updatedKyc.id,
        identityType: updatedKyc.identityType as IdentityType,
        identityNumber: updatedKyc.identityNumber,
        birthdate: updatedKyc.birthdate,
        birthplace: updatedKyc.birthplace,
        address: updatedKyc.address,
        state: updatedKyc.state,
        country: updatedKyc.country,
        zipNumber: updatedKyc.zipNumber,
        phoneNumber: updatedKyc.phoneNumber,
        status: updatedKyc.status as KycStatus,
        provider: updatedKyc.provider,
        fileName: updatedKyc.fileName,
        userId: updatedKyc.userId,
        createdAt: updatedKyc.createdAt,
        updatedAt: updatedKyc.updatedAt,
      });
    } catch (error) {
      this.handleServiceError(
        error,
        'Failed to update KYC status from frontend',
      );
    }
  }

  /**
   * Map Sumsub review status to KYC status using constants
   */
  private mapSumsubStatusToKycStatus(sumsubStatus: string): KycStatus {
    return SUMSUB_STATUS_MAPPING[sumsubStatus] || KycStatus.MANUAL_REVIEW;
  }

  /**
   * Get KYC status for Sumsub integration
   */
  async getSumsubKycStatus(userId: string): Promise<{
    status: KycStatus;
    canProceed: boolean;
    message: string;
  }> {
    try {
      const kyc = await this.prisma.kyc.findUnique({
        where: { userId },
        select: {
          status: true,
          provider: true,
          updatedAt: true,
        },
      });

      if (!kyc) {
        return {
          status: KycStatus.PENDING,
          canProceed: false,
          message: 'No KYC record found. Please initiate KYC process.',
        };
      }

      const status = kyc.status as KycStatus;
      const canProceed = status === KycStatus.APPROVED;
      const message = this.getStatusMessage(status);

      return {
        status,
        canProceed,
        message,
      };
    } catch (error) {
      this.handleServiceError(error, 'Failed to get Sumsub KYC status');
    }
  }

  /**
   * Handle service errors
   */
  private handleServiceError(error: any, defaultMessage: string): never {
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException ||
      error instanceof NotFoundException ||
      error instanceof ForbiddenException
    ) {
      throw error;
    }

    console.error('KYC Service Error:', error);

    if (error.code === 'P2002') {
      throw new ConflictException('Data KYC sudah ada');
    }

    if (error.code === 'P2025') {
      throw new NotFoundException('Data tidak ditemukan');
    }

    throw new InternalServerErrorException(defaultMessage);
  }
}

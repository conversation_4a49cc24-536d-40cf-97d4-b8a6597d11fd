import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { KycService } from './kyc.service';
import { CreateKycDto } from './dto/create-kyc.dto';
import { UpdateKycDto } from './dto/update-kyc.dto';
import { KycResponseDto, KycStatusResponseDto } from './dto/kyc-response.dto';
import {
  UpdateKycStatusDto,
  SumsubStatusResponseDto,
} from './dto/sumsub-webhook.dto';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';

@ApiTags('KYC (Know Your Customer)')
@Controller('api/v1/kyc')
@UseGuards(EnhancedJwtAuthGuard)
@ApiBearerAuth('JWT')
export class KycController {
  constructor(private readonly kycService: KycService) {}

  @Post('initiate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Initiate KYC process',
    description:
      'Initiates the Know Your Customer verification process for the authenticated user. This endpoint creates a new KYC record and can integrate with third-party providers like Sumsub.',
  })
  @ApiBody({ type: CreateKycDto })
  @ApiResponse({
    status: 201,
    description: 'KYC process successfully initiated',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid or incomplete data',
    schema: {
      example: {
        statusCode: 400,
        message: 'Tanggal lahir tidak boleh di masa depan',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or missing authentication token',
    schema: {
      example: {
        statusCode: 401,
        message: 'Authentication failed',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'User already has KYC data',
    schema: {
      example: {
        statusCode: 409,
        message: 'User sudah memiliki data KYC',
        error: 'Conflict',
      },
    },
  })
  async initiateKyc(
    @Body(ValidationPipe) createKycDto: CreateKycDto,
    @Request() req: any,
  ): Promise<KycResponseDto> {
    return this.kycService.initiateKyc(createKycDto, req.user.sub);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create KYC data (Legacy)',
    description:
      'Creates Know Your Customer data for the authenticated user. This endpoint is maintained for backward compatibility. Use /initiate for new implementations.',
    deprecated: true,
  })
  @ApiBody({ type: CreateKycDto })
  @ApiResponse({
    status: 201,
    description: 'KYC data successfully created',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid or incomplete data',
    schema: {
      example: {
        statusCode: 400,
        message: 'Tanggal lahir tidak boleh di masa depan',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or missing authentication token',
    schema: {
      example: {
        statusCode: 401,
        message: 'Authentication failed',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'User already has KYC data',
    schema: {
      example: {
        statusCode: 409,
        message: 'User sudah memiliki data KYC',
        error: 'Conflict',
      },
    },
  })
  async create(
    @Body(ValidationPipe) createKycDto: CreateKycDto,
    @Request() req: any,
  ): Promise<KycResponseDto> {
    return this.kycService.create(createKycDto, req.user.sub);
  }

  @Get()
  @ApiOperation({
    summary: 'Dapatkan data KYC',
    description: 'Mengambil data KYC milik user yang sedang login',
  })
  @ApiResponse({
    status: 200,
    description: 'Data KYC berhasil diambil',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
    schema: {
      example: {
        statusCode: 401,
        message: 'Authentication failed',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
    schema: {
      example: {
        statusCode: 404,
        message: 'Data KYC tidak ditemukan',
        error: 'Not Found',
      },
    },
  })
  async findOwn(@Request() req: any): Promise<KycResponseDto> {
    return this.kycService.findByUserId(req.user.sub);
  }

  @Get('status')
  @ApiOperation({
    summary: 'Dapatkan status KYC',
    description: 'Mengambil status verifikasi KYC milik user yang sedang login',
  })
  @ApiResponse({
    status: 200,
    description: 'Status KYC berhasil diambil',
    type: KycStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
  })
  async getStatus(@Request() req: any): Promise<KycStatusResponseDto> {
    return this.kycService.getStatus(req.user.sub);
  }

  @Put()
  @ApiOperation({
    summary: 'Update data KYC',
    description:
      'Memperbarui data KYC milik user yang sedang login. User hanya dapat mengupdate data jika status KYC adalah PENDING, REJECTED, atau DENIED',
  })
  @ApiBody({ type: UpdateKycDto })
  @ApiResponse({
    status: 200,
    description: 'Data KYC berhasil diperbarui',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Data tidak valid',
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
  })
  @ApiResponse({
    status: 403,
    description:
      'Data KYC tidak dapat diubah karena sedang dalam proses verifikasi atau sudah disetujui',
    schema: {
      example: {
        statusCode: 403,
        message:
          'Data KYC tidak dapat diubah karena sedang dalam proses verifikasi atau sudah disetujui',
        error: 'Forbidden',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
  })
  async update(
    @Body(ValidationPipe) updateKycDto: UpdateKycDto,
    @Request() req: any,
  ): Promise<KycResponseDto> {
    return this.kycService.update(req.user.sub, updateKycDto, false);
  }

  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hapus data KYC',
    description:
      'Menghapus data KYC milik user yang sedang login. Hanya dapat dilakukan jika status KYC adalah PENDING, REJECTED, atau DENIED',
  })
  @ApiResponse({
    status: 200,
    description: 'Data KYC berhasil dihapus',
    schema: {
      example: {
        message: 'Data KYC berhasil dihapus',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
  })
  @ApiResponse({
    status: 403,
    description: 'Data KYC tidak dapat dihapus',
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
  })
  async delete(@Request() req: any): Promise<{ message: string }> {
    return this.kycService.delete(req.user.sub);
  }

  // Admin-only endpoints (akan ditambahkan jika diperlukan)
  @Get('admin/user/:userId')
  @ApiOperation({
    summary: 'Dapatkan data KYC user (Admin only)',
    description:
      'Mengambil data KYC user tertentu. Hanya dapat diakses oleh admin',
  })
  @ApiParam({
    name: 'userId',
    description: 'ID user yang akan diambil data KYC-nya',
    example: '01234567',
  })
  @ApiResponse({
    status: 200,
    description: 'Data KYC berhasil diambil',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
  })
  @ApiResponse({
    status: 403,
    description: 'Akses ditolak - hanya admin',
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
  })
  async findByUserId(@Param('userId') userId: string): Promise<KycResponseDto> {
    // TODO: Add admin role check here
    // For now, allow any authenticated user for demonstration
    return this.kycService.findByUserId(userId);
  }

  @Put('admin/user/:userId')
  @ApiOperation({
    summary: 'Update data KYC user (Admin only)',
    description:
      'Memperbarui data KYC user tertentu. Hanya dapat diakses oleh admin. Admin dapat mengubah status KYC',
  })
  @ApiParam({
    name: 'userId',
    description: 'ID user yang akan diupdate data KYC-nya',
    example: '01234567',
  })
  @ApiBody({ type: UpdateKycDto })
  @ApiResponse({
    status: 200,
    description: 'Data KYC berhasil diperbarui',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Data tidak valid',
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
  })
  @ApiResponse({
    status: 403,
    description: 'Akses ditolak - hanya admin',
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
  })
  async updateByAdmin(
    @Param('userId') userId: string,
    @Body(ValidationPipe) updateKycDto: UpdateKycDto,
  ): Promise<KycResponseDto> {
    // TODO: Add admin role check here
    // For now, treat as admin for demonstration
    return this.kycService.update(userId, updateKycDto, true);
  }

  @Delete('admin/user/:userId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hapus data KYC user (Admin only)',
    description:
      'Menghapus data KYC user tertentu. Hanya dapat diakses oleh admin',
  })
  @ApiParam({
    name: 'userId',
    description: 'ID user yang akan dihapus data KYC-nya',
    example: '01234567',
  })
  @ApiResponse({
    status: 200,
    description: 'Data KYC berhasil dihapus',
    schema: {
      example: {
        message: 'Data KYC berhasil dihapus',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Token tidak valid atau tidak ada',
  })
  @ApiResponse({
    status: 403,
    description: 'Akses ditolak - hanya admin',
  })
  @ApiResponse({
    status: 404,
    description: 'Data KYC tidak ditemukan',
  })
  async deleteByAdmin(
    @Param('userId') userId: string,
  ): Promise<{ message: string }> {
    // TODO: Add admin role check here
    // For now, allow any authenticated user for demonstration
    return this.kycService.delete(userId);
  }

  // Frontend integration endpoints
  @Put('status/update')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update KYC status from frontend',
    description:
      'Updates KYC status based on Sumsub verification results from frontend',
  })
  @ApiBody({ type: UpdateKycStatusDto })
  @ApiResponse({
    status: 200,
    description: 'KYC status updated successfully',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid status data',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'KYC status cannot be updated in current state',
  })
  @ApiResponse({
    status: 404,
    description: 'KYC record not found',
  })
  async updateKycStatusFromFrontend(
    @Body(ValidationPipe) statusData: UpdateKycStatusDto,
    @Request() req: any,
  ): Promise<KycResponseDto> {
    return this.kycService.updateKycStatusFromFrontend(
      req.user.sub,
      statusData,
    );
  }

  @Get('status/verification')
  @ApiOperation({
    summary: 'Get KYC verification status',
    description:
      'Retrieves current KYC verification status for the authenticated user, including readiness for operations',
  })
  @ApiResponse({
    status: 200,
    description: 'KYC verification status retrieved successfully',
    type: SumsubStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 404,
    description: 'KYC record not found',
  })
  async getKycVerificationStatus(
    @Request() req: any,
  ): Promise<SumsubStatusResponseDto> {
    return this.kycService.getSumsubKycStatus(req.user.sub);
  }
}

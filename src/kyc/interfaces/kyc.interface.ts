import { KycStatus, IdentityType } from '../dto/create-kyc.dto';

/**
 * Interface for KYC entity from database
 */
export interface KycEntity {
  id: string;
  identityType: string;
  identityNumber: string;
  birthdate: Date;
  birthplace: string;
  address: string;
  state: string;
  country: string;
  zipNumber: string;
  phoneNumber: string;
  status: string;
  provider?: string | null;
  fileName?: string | null;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for user entity (minimal required fields)
 */
export interface UserEntity {
  id: string;
  status: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for KYC status update from frontend
 */
export interface KycStatusUpdateData {
  reviewStatus: string;
  applicantId?: string;
  additionalData?: Record<string, any>;
}

/**
 * Interface for KYC verification status response
 */
export interface KycVerificationStatus {
  status: KycStatus;
  canProceed: boolean;
  message: string;
  provider?: string;
  updatedAt?: Date;
}

/**
 * Interface for status transition validation
 */
export interface StatusTransition {
  from: KycStatus;
  to: KycStatus;
}

/**
 * Interface for KYC validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Interface for KYC business rules
 */
export interface KycBusinessRules {
  minAge: number;
  maxAge: number;
  supportedCountries: string[];
  supportedProviders: string[];
  allowedFileExtensions: string[];
  maxFileNameLength: number;
  minAddressLength: number;
  maxAddressLength: number;
}

/**
 * Interface for KYC service configuration
 */
export interface KycServiceConfig {
  businessRules: KycBusinessRules;
  enableLogging: boolean;
  enableValidation: boolean;
}

/**
 * Type for KYC operation result
 */
export type KycOperationResult<T> = {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
};

/**
 * Type for status transition map
 */
export type StatusTransitionMap = Record<KycStatus, KycStatus[]>;

/**
 * Type for validation function
 */
export type ValidationFunction<T> = (data: T) => ValidationResult;

/**
 * Enum for KYC operation types
 */
export enum KycOperationType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  STATUS_UPDATE = 'STATUS_UPDATE',
  VERIFICATION = 'VERIFICATION',
}

/**
 * Interface for KYC audit log
 */
export interface KycAuditLog {
  id: string;
  userId: string;
  operation: KycOperationType;
  oldStatus?: KycStatus;
  newStatus?: KycStatus;
  performedBy: string;
  isAdmin: boolean;
  timestamp: Date;
  metadata?: Record<string, any>;
}

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject } from 'class-validator';

export class SumsubWebhookDto {
  @ApiProperty({
    example: 'applicant-123456',
    description: 'Sumsub applicant ID',
  })
  @IsString()
  applicantId: string;

  @ApiProperty({
    example: 'user-789',
    description: 'External user ID (our user ID)',
  })
  @IsString()
  externalUserId: string;

  @ApiProperty({
    example: 'completed',
    description: 'Review status from Sumsub',
    enum: ['init', 'pending', 'queued', 'completed', 'approved', 'rejected', 'onHold', 'final_rejected'],
  })
  @IsString()
  reviewStatus: string;

  @ApiProperty({
    example: 'GREEN',
    description: 'Review result from Sumsub',
    required: false,
  })
  @IsOptional()
  @IsString()
  reviewResult?: string;

  @ApiProperty({
    example: 'identity',
    description: 'Type of verification',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Additional webhook data from Sumsub',
    required: false,
  })
  @IsOptional()
  @IsObject()
  additionalData?: Record<string, any>;
}

export class SumsubStatusResponseDto {
  @ApiProperty({
    example: 'APPROVED',
    description: 'Current KYC status',
  })
  status: string;

  @ApiProperty({
    example: true,
    description: 'Whether user can proceed with operations',
  })
  canProceed: boolean;

  @ApiProperty({
    example: 'KYC telah disetujui',
    description: 'Status message',
  })
  message: string;

  @ApiProperty({
    example: 'SUMSUB',
    description: 'KYC provider',
    required: false,
  })
  provider?: string;

  @ApiProperty({
    example: '2023-01-16T15:30:00.000Z',
    description: 'Last updated timestamp',
    required: false,
  })
  updatedAt?: Date;
}

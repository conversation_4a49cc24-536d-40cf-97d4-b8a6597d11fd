import { PartialType } from '@nestjs/swagger';
import { CreateKycDto, KycStatus } from './create-kyc.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateKycDto extends PartialType(CreateKycDto) {
  @ApiProperty({
    enum: KycStatus,
    example: KycStatus.APPROVED,
    description: 'Status verifikasi KYC (hanya untuk admin)',
    required: false,
  })
  @IsOptional()
  @IsEnum(KycStatus, {
    message:
      'Status KYC harus berupa PENDING, APPROVED, REJECTED, DENIED, CANCELED, MANUAL_REVIEW, atau LOCKED',
  })
  status?: KycStatus;

  @ApiProperty({
    example: 'SUMSUB',
    description: 'Provider layanan KYC yang digunakan untuk verifikasi',
    required: false,
  })
  @IsOptional()
  provider?: string;
}
